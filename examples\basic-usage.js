/**
 * Basic Usage Example
 * Demonstrates how to connect to ODude contracts and perform basic operations
 */

const ODudeSDK = require('../src/index');

async function main() {
  console.log('=== ODude SDK Basic Usage Example ===\n');

  // Initialize SDK with localhost
  const sdk = new ODudeSDK({
    rpcUrl: 'https://base-sepolia.infura.io/v3/********************************'
  });

  // Connect to deployed contracts (using network config)
  sdk.connectNetwork('basesepolia');
  console.log('✓ Connected to Base Sepolia contracts\n');

  // === Registry Contract ===
  console.log('--- Registry Contract ---');
  
  const registryName = await sdk.registry.name();
  const registrySymbol = await sdk.registry.symbol();
  const totalSupply = await sdk.registry.totalSupply();
  
  console.log('Contract Name:', registryName);
  console.log('Contract Symbol:', registrySymbol);
  console.log('Total Supply:', totalSupply.toString());
  console.log('Registry Address:', sdk.registry.address);
  console.log();

  // === Resolver Contract ===
  console.log('--- Resolver Contract ---');
  
  const resolverAddress = sdk.resolver.address;
  console.log('Resolver Address:', resolverAddress);
  
  // Try to resolve a name
  try {
    const exists = await sdk.resolver.nameExists('bitcoin@crypto');
    console.log('Name "bitcoin@crypto" exists:', exists);
  } catch (error) {
    console.log('Name resolution check failed:', error.message);
  }
  console.log();

  // === TLD Contract ===
  console.log('--- TLD Contract ---');
  
  const baseTLDPrice = await sdk.tld.getBaseTLDPrice();
  const defaultCommission = await sdk.tld.getDefaultCommission();
  
  console.log('TLD Address:', sdk.tld.address);
  console.log('Base TLD Price:', sdk.utils.formatEther(baseTLDPrice), 'ETH');
  console.log('Default Commission:', defaultCommission.toString(), '%');
  console.log();

  // === RWAirdrop Contract ===
  console.log('--- RWAirdrop Contract ---');
  
  console.log('RWAirdrop Address:', sdk.rwairdrop.address);
  
  try {
    const isActive = await sdk.rwairdrop.isAirdropActive();
    console.log('Airdrop Active:', isActive);
  } catch (error) {
    console.log('Airdrop status check failed:', error.message);
  }
  console.log();

  // === Helper Utilities ===
  console.log('--- Helper Utilities ---');

  const testName = 'bitcoin@crypto';
  const normalized = sdk.utils.normalizeName(testName);
  const parsed = sdk.utils.parseName(normalized);

  console.log('Original name:', testName);
  console.log('Normalized:', normalized);
  console.log('Parsed:', parsed);
  console.log();

  console.log('✓ Example completed successfully!');
}

// Run the example
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });

