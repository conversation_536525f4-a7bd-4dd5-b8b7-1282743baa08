# ODude SDK

A developer-friendly npm package for interacting with the ODude smart contracts ecosystem. This SDK provides easy-to-use wrappers for Registry, Resolver, TLD, and RWAirdrop contracts.

## Features

- 🚀 Simple and intuitive API
- 📦 Full TypeScript support (types coming soon)
- 🔧 Built on ethers.js v6
- 🧪 Comprehensive test coverage
- 📚 Well-documented with examples
- 🎯 Helper utilities for name parsing and formatting

## Installation

```bash
npm install odude-sdk
```

## Quick Start

### Multi-Network Configuration

The ODude SDK now supports multiple networks with automatic TLD-based routing:

```javascript
const ODudeSDK = require('odude-sdk');

// Initialize SDK with multiple network RPC URLs
const sdk = new ODudeSDK({
  rpcUrl_filecoin: 'https://api.node.glif.io',
  rpcUrl_bnb: 'https://bsc-dataseed1.binance.org',
  rpcUrl_sepolia: 'https://sepolia.base.org'
});

// Connect to all networks
sdk.connectAllNetworks();

// The SDK automatically routes based on TLD:
// - .fil, .fvm domains → Filecoin network
// - .bnb, .binance domains → BNB Smart Chain
// - Other domains → Base Sepolia (default)

// Resolve names automatically routes to correct network
const address = await sdk.resolve('alice@fil'); // Uses Filecoin network
const bnbAddress = await sdk.resolve('bob@bnb'); // Uses BNB network
```

### Environment Variable Configuration

You can also configure RPC URLs using environment variables:

```bash
# Set environment variables
export FILECOIN_RPC_URL="https://your-filecoin-rpc.com"
export BNB_RPC_URL="https://your-bnb-rpc.com"
export BASE_SEPOLIA_RPC_URL="https://your-base-sepolia-rpc.com"
```

```javascript
// SDK will automatically use environment variables
const sdk = new ODudeSDK();
sdk.connectAllNetworks();
```

### Connect to Localhost

```javascript
const ODudeSDK = require('odude-sdk');

// Initialize SDK with localhost
const sdk = new ODudeSDK({
  rpcUrl: 'http://127.0.0.1:8545'
});

// Connect to deployed contracts
sdk.connectLocalhost();

// Now you can use all contracts
const totalSupply = await sdk.registry.totalSupply();
console.log('Total names registered:', totalSupply.toString());
```

### Connect to Specific Network

```javascript
const ODudeSDK = require('odude-sdk');

// Connect to a specific network
const sdk = new ODudeSDK();
sdk.connectNetwork('basesepolia'); // or 'filecoin', 'bnb', 'localhost'

// Access contracts for the connected network
const registry = sdk.registry;
const resolver = sdk.resolver;
```

### With Signer (for Write Operations)

```javascript
const { Wallet } = require('ethers');
const ODudeSDK = require('odude-sdk');

const sdk = new ODudeSDK({
  rpcUrl: 'http://127.0.0.1:8545',
  privateKey: 'YOUR_PRIVATE_KEY'
});

sdk.connectLocalhost();

// Now you can perform write operations
const tx = await sdk.registry.setReverse(tokenId);
await tx.wait();
```

## Usage Examples

### Registry Contract

```javascript
// Get name information
const tokenId = await sdk.registry.getTokenId('alice@odude');
const owner = await sdk.registry.ownerOf(tokenId);
const metadata = await sdk.registry.getNFTMetadata(tokenId);

console.log('Token ID:', tokenId.toString());
console.log('Owner:', owner);
console.log('Metadata:', metadata);

// Get owner by name
const ownerAddress = await sdk.registry.getOwnerByName('alice@odude');

// Get name by token ID
const name = await sdk.registry.nameOf(tokenId);

// Get total supply
const totalSupply = await sdk.registry.totalSupply();

// Claim a name (requires signer)
const tx = await sdk.registry.claim(
  tokenId,
  'myname@odude',
  'ipfs://...',
  recipientAddress,
  { value: ethers.parseEther('0.1') }
);
await tx.wait();

// Set reverse resolution
const reverseTx = await sdk.registry.setReverse(tokenId);
await reverseTx.wait();

// Listen to events
sdk.registry.onTLDMinted((tokenId, name, owner) => {
  console.log('New TLD minted:', name, 'by', owner);
});
```

### Resolver Contract

```javascript
// Resolve name to address
const address = await sdk.resolver.resolve('alice@odude');
console.log('Resolved address:', address);

// Reverse resolve address to name
const name = await sdk.resolver.reverse('0x...');
console.log('Primary name:', name);

// Check if name exists
const exists = await sdk.resolver.nameExists('alice@odude');

// Get resolution record
const record = await sdk.resolver.getResolutionRecord('alice@odude');
console.log('Resolution record:', record);
// {
//   resolvedAddress: '0x...',
//   name: 'alice.odude',
//   tokenId: 123n,
//   exists: true
// }

// Get reverse record
const reverseRecord = await sdk.resolver.getReverseRecord('0x...');
console.log('Reverse record:', reverseRecord);
// {
//   primaryName: 'alice.odude',
//   primaryTokenId: 123n,
//   exists: true
// }

// Check if address has reverse
const hasReverse = await sdk.resolver.hasReverse('0x...');
```

### TLD Contract

```javascript
// Get base TLD price
const basePrice = await sdk.tld.getBaseTLDPrice();
console.log('Base TLD price:', ethers.formatEther(basePrice), 'ETH');

// Get TLD-specific price
const tldPrice = await sdk.tld.getTLDPrice(tldTokenId);

// Get commission rate
const commission = await sdk.tld.getCommission(tldTokenId);
console.log('Commission:', commission.toString(), '%');

// Get TLD owner
const tldOwner = await sdk.tld.getTLDOwner(tldTokenId);

// Check if TLD is active
const isActive = await sdk.tld.isTLDActive(tldTokenId);

// Get TLD name
const tldName = await sdk.tld.getTLDName(tldTokenId);

// Set TLD price (requires signer and ownership)
const tx = await sdk.tld.setTLDPrice(tldTokenId, ethers.parseEther('0.05'));
await tx.wait();

// Set commission (requires signer and ownership)
const commissionTx = await sdk.tld.setCommission(tldTokenId, 15);
await commissionTx.wait();

// New TLD Management Functions
// Set ERC token for a TLD
const setErcTx = await sdk.tld.setErcToken(tldTokenId, '0x...');
await setErcTx.wait();

// Get ERC token for a TLD
const ercToken = await sdk.tld.getErcToken(tldTokenId);

// Set token price for a TLD
const setPriceTx = await sdk.tld.setTokenPrice(tldTokenId, ethers.parseEther('0.1'));
await setPriceTx.wait();

// Get token price for a TLD
const tokenPrice = await sdk.tld.getTokenPrice(tldTokenId);

// Domain Minting Functions
// Check if domain is eligible for minting
const eligibility = await sdk.tld.checkMintEligibility('alice.fil');
console.log('Eligibility:', eligibility);
// {
//   eligible: true,
//   available: true,
//   tldActive: true,
//   cost: 500000000000000000n,
//   reason: 'Eligible for minting'
// }

// Estimate minting cost
const cost = await sdk.tld.estimateMintCost('alice.fil');
console.log('Minting cost:', ethers.formatEther(cost), 'ETH');

// Mint domain
const mintTx = await sdk.tld.mintDomain(
  'alice.fil',
  '0x...',
  { value: cost }
);
await mintTx.wait();
```

### RWAirdrop Contract

```javascript
// Check if address is eligible
const isEligible = await sdk.rwairdrop.isEligible('0x...');

// Get claimable amount
const claimableAmount = await sdk.rwairdrop.getClaimableAmount('0x...');
console.log('Claimable:', ethers.formatEther(claimableAmount), 'tokens');

// Check if already claimed
const hasClaimed = await sdk.rwairdrop.hasClaimed('0x...');

// Get comprehensive airdrop info
const airdropInfo = await sdk.rwairdrop.getAirdropInfo('0x...');
console.log('Airdrop info:', airdropInfo);
// {
//   eligible: true,
//   claimed: false,
//   claimableAmount: 1000000000000000000n,
//   isActive: true,
//   canClaim: true
// }

// Claim airdrop (requires signer)
if (airdropInfo.canClaim) {
  const tx = await sdk.rwairdrop.claim();
  await tx.wait();
  console.log('Airdrop claimed!');
}

// Get airdrop statistics
const totalClaimed = await sdk.rwairdrop.getTotalClaimed();
const totalAmount = await sdk.rwairdrop.getTotalAirdropAmount();
console.log('Progress:', ethers.formatEther(totalClaimed), '/', ethers.formatEther(totalAmount));
```

### Helper Utilities

```javascript
const { utils } = sdk;

// Normalize name
const normalized = utils.normalizeName('ALICE.ODUDE');
// 'alice.odude'

// Extract TLD
const tld = utils.extractTLD('alice.odude');
// 'odude'

// Extract subdomain
const subdomain = utils.extractSubdomain('alice.odude');
// 'alice'

// Check if TLD
const isTLD = utils.isTLD('odude');
// true

// Check if subdomain
const isSubdomain = utils.isSubdomain('alice.odude');
// true

// Parse name
const parsed = utils.parseName('bob.alice.odude');
// {
//   full: 'bob.alice.odude',
//   tld: 'odude',
//   subdomain: 'bob.alice',
//   parts: ['bob', 'alice', 'odude'],
//   isTLD: false,
//   isSubdomain: true
// }

// Validate address
const isValid = utils.isValidAddress('0x...');

// Format token ID
const formatted = utils.formatTokenId(123n);
// '123'

// Parse and format ether
const wei = utils.parseEther('1.0');
const ether = utils.formatEther(wei);
```

### Convenience Methods

```javascript
// Quick resolve
const address = await sdk.resolve('alice.odude');

// Quick reverse
const name = await sdk.reverse('0x...');

// Get owner
const owner = await sdk.getOwner('alice.odude');

// Get comprehensive name info
const nameInfo = await sdk.getNameInfo('alice.odude');
console.log(nameInfo);
// {
//   name: 'alice.odude',
//   tokenId: 123n,
//   owner: '0x...',
//   metadata: { ... },
//   tokenURI: 'ipfs://...',
//   resolvedAddress: '0x...',
//   exists: true
// }

// Get airdrop info
const airdropInfo = await sdk.getAirdropInfo('0x...');
```

### Batch Operations

The SDK supports efficient batch operations for multiple queries:

```javascript
// Registry batch operations
const tokenIds = [1, 2, 3, 4, 5];

// Get multiple names at once
const names = await sdk.registry.getMultipleNames(tokenIds);
console.log('Names:', names);

// Get multiple owners at once
const owners = await sdk.registry.getMultipleOwners(tokenIds);
console.log('Owners:', owners);

// Get comprehensive info for multiple tokens
const tokenInfo = await sdk.registry.getMultipleTokenInfo(tokenIds);
console.log('Token info:', tokenInfo);

// Resolver batch operations
const domainNames = ['alice.fil', 'bob.bnb', 'charlie.odude'];

// Resolve multiple names at once
const resolutions = await sdk.resolver.resolveMultiple(domainNames);
console.log('Resolutions:', resolutions);
// [
//   { name: 'alice.fil', address: '0x...', resolved: true },
//   { name: 'bob.bnb', address: '0x...', resolved: true },
//   { name: 'charlie.odude', address: null, resolved: false, error: '...' }
// ]

// Reverse resolve multiple addresses
const addresses = ['0x...', '0x...', '0x...'];
const reverseResolutions = await sdk.resolver.reverseMultiple(addresses);

// Check multiple names existence
const existenceResults = await sdk.resolver.checkMultipleNamesExist(domainNames);
```

### Event Monitoring

Listen to real-time events across networks:

```javascript
// Listen to Transfer events
sdk.onTransfer((from, to, tokenId) => {
  console.log(`Transfer: ${from} → ${to} (Token: ${tokenId})`);
});

// Listen to NameResolved events
sdk.onNameResolved((name, address) => {
  console.log(`Name resolved: ${name} → ${address}`);
});

// Listen to DomainMinted events
sdk.onDomainMinted((name, owner) => {
  console.log(`Domain minted: ${name} by ${owner}`);
});

// Listen to events across all networks
sdk.onTransferAllNetworks((from, to, tokenId, network) => {
  console.log(`Transfer on ${network}: ${from} → ${to} (Token: ${tokenId})`);
});

// Remove all event listeners
sdk.removeAllListeners();

// Remove listeners for specific network
sdk.removeAllListeners('filecoin');
```

### Error Handling

The SDK provides specific error types for better error handling:

```javascript
const {
  TokenNotFoundError,
  NameNotFoundError,
  NetworkError,
  UnsupportedTLDError,
  MintingError
} = require('odude-sdk');

try {
  const address = await sdk.resolve('nonexistent.fil');
} catch (error) {
  if (error instanceof NameNotFoundError) {
    console.log('Name not found:', error.name);
  } else if (error instanceof UnsupportedTLDError) {
    console.log('Unsupported TLD:', error.tld);
  } else if (error instanceof NetworkError) {
    console.log('Network error:', error.networkName);
  }
}

try {
  await sdk.tld.mintDomain('alice.fil', '0x...');
} catch (error) {
  if (error instanceof MintingError) {
    console.log('Minting failed:', error.domainName, error.message);
  }
}
```

## Testing

Run the test suite:

```bash
# Install dependencies
npm install

# Verify setup
npm run verify

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run examples
npm run example:basic
npm run example:resolve
npm run example:airdrop
npm run example:tld
```

Make sure you have a local Hardhat node running with the ODude contracts deployed:

```bash
# In your ODude contracts repository
npx hardhat node
```

## API Reference

### ODudeSDK

Main SDK class that provides access to all contracts.

#### Constructor Options

- `rpcUrl` (string): RPC URL for the network
- `provider` (ethers.Provider): Custom provider instance
- `signer` (ethers.Signer): Signer for write operations
- `privateKey` (string): Private key to create a signer

#### Methods

- `connect(addresses)`: Connect to contracts with custom addresses
- `connectLocalhost()`: Connect using localhost-deployment.json
- `connectSigner(signer)`: Connect a signer for write operations

#### Properties

- `registry`: Registry contract instance
- `resolver`: Resolver contract instance
- `tld`: TLD contract instance
- `rwairdrop`: RWAirdrop contract instance
- `utils`: Helper utilities
- `provider`: Ethers provider
- `signer`: Ethers signer (if connected)

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

