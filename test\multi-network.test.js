const { expect } = require('chai');
const { JsonRpcProvider } = require('ethers');
const ODudeSDK = require('../src/ODudeSDK');
const { NetworkError, ConfigurationError, UnsupportedTLDError } = require('../src/utils/errors');

describe('Multi-Network Support', function() {
  let sdk;

  describe('Network Configuration', function() {
    it('should load network configuration on initialization', function() {
      sdk = new ODudeSDK();
      expect(sdk.networkConfig).to.exist;
      expect(sdk.networkConfig.networks).to.exist;
      expect(sdk.networkConfig.tldMappings).to.exist;
      expect(sdk.networkConfig.defaultNetwork).to.exist;
    });

    it('should validate multi-network RPC URL configuration', function() {
      expect(() => {
        new ODudeSDK({
          rpcUrl_filecoin: 'https://api.node.glif.io',
          rpcUrl_bnb: 'https://bsc-dataseed1.binance.org',
          rpcUrl_sepolia: 'https://sepolia.base.org'
        });
      }).to.not.throw();
    });

    it('should throw ConfigurationError for invalid RPC URL', function() {
      expect(() => {
        new ODudeSDK({
          rpcUrl_filecoin: 123 // Invalid type
        });
      }).to.throw(ConfigurationError);
    });
  });

  describe('TLD to Network Mapping', function() {
    beforeEach(function() {
      sdk = new ODudeSDK();
    });

    it('should map "fil" TLD to filecoin network', function() {
      const network = sdk._getNetworkForTLD('fil');
      expect(network).to.equal('filecoin');
    });

    it('should map "fvm" TLD to filecoin network', function() {
      const network = sdk._getNetworkForTLD('fvm');
      expect(network).to.equal('filecoin');
    });

    it('should map "bnb" TLD to bnb network', function() {
      const network = sdk._getNetworkForTLD('bnb');
      expect(network).to.equal('bnb');
    });

    it('should map "binance" TLD to bnb network', function() {
      const network = sdk._getNetworkForTLD('binance');
      expect(network).to.equal('bnb');
    });

    it('should map unknown TLD to default network', function() {
      const network = sdk._getNetworkForTLD('unknown');
      expect(network).to.equal('basesepolia');
    });
  });

  describe('Provider Management', function() {
    it('should get provider for specific network', function() {
      sdk = new ODudeSDK({
        rpcUrl_filecoin: 'https://api.node.glif.io'
      });
      
      const provider = sdk.getProvider('filecoin');
      expect(provider).to.be.instanceOf(JsonRpcProvider);
    });

    it('should get provider for TLD', function() {
      sdk = new ODudeSDK({
        rpcUrl_filecoin: 'https://api.node.glif.io'
      });
      
      const provider = sdk.getProvider('fil');
      expect(provider).to.be.instanceOf(JsonRpcProvider);
    });

    it('should throw NetworkError for unavailable network', function() {
      sdk = new ODudeSDK();
      
      expect(() => {
        sdk.getProvider('nonexistent');
      }).to.throw(NetworkError);
    });
  });

  describe('Network Connection', function() {
    beforeEach(function() {
      sdk = new ODudeSDK();
    });

    it('should connect to specific network', function() {
      expect(() => {
        sdk.connectNetwork('localhost');
      }).to.not.throw();
      expect(sdk.currentNetwork).to.equal('localhost');
    });

    it('should throw NetworkError for invalid network', function() {
      expect(() => {
        sdk.connectNetwork('invalid_network');
      }).to.throw(NetworkError);
    });

    it('should connect to default network when no network specified', function() {
      sdk.connectNetwork();
      expect(sdk.currentNetwork).to.equal('basesepolia');
    });
  });

  describe('Contract Access', function() {
    beforeEach(function() {
      sdk = new ODudeSDK();
      sdk.connectNetwork('localhost');
    });

    it('should access registry for specific network', function() {
      expect(() => {
        const registry = sdk.registry;
        expect(registry).to.exist;
      }).to.not.throw();
    });

    it('should access resolver for TLD', function() {
      expect(() => {
        const resolver = sdk.resolver;
        expect(resolver).to.exist;
      }).to.not.throw();
    });

    it('should use current network when no network specified', function() {
      expect(() => {
        const tld = sdk.tld;
        expect(tld).to.exist;
      }).to.not.throw();
    });
  });

  describe('Environment Variable Support', function() {
    it('should use environment variable for RPC URL', function() {
      // Mock environment variable
      process.env.FILECOIN_RPC_URL = 'https://custom-filecoin-rpc.com';
      
      sdk = new ODudeSDK();
      const rpcUrl = sdk._getRpcUrlForNetwork('filecoin');
      expect(rpcUrl).to.equal('https://custom-filecoin-rpc.com');
      
      // Clean up
      delete process.env.FILECOIN_RPC_URL;
    });

    it('should fallback to default RPC URL when env var not set', function() {
      sdk = new ODudeSDK();
      const rpcUrl = sdk._getRpcUrlForNetwork('filecoin');
      expect(rpcUrl).to.equal('https://api.node.glif.io');
    });
  });

  describe('Error Handling', function() {
    beforeEach(function() {
      sdk = new ODudeSDK();
    });

    it('should throw UnsupportedTLDError for resolve with unsupported TLD', async function() {
      try {
        await sdk.resolve('test.unsupported');
        expect.fail('Should have thrown UnsupportedTLDError');
      } catch (error) {
        expect(error).to.be.instanceOf(UnsupportedTLDError);
      }
    });

    it('should handle invalid domain format in resolve', async function() {
      try {
        await sdk.resolve('invalid_domain');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Invalid domain format');
      }
    });
  });
});
