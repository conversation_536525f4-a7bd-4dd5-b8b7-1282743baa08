const { expect } = require('chai');
const { JsonRpcProvider } = require('ethers');
const Registry = require('../src/contracts/Registry');

describe('Registry Contract', function() {
  let registry;
  let provider;
  const REGISTRY_ADDRESS = '******************************************';

  before(async function() {
    // Connect to local hardhat node
    provider = new JsonRpcProvider('https://base-sepolia.infura.io/v3/********************************');
    registry = new Registry(REGISTRY_ADDRESS, provider);
  });

  describe('Read-only functions', function() {
    it('should get contract name', async function() {
      const name = await registry.name();
      expect(name).to.be.a('string');
      console.log('Contract name:', name);
    });

    it('should get contract symbol', async function() {
      const symbol = await registry.symbol();
      expect(symbol).to.be.a('string');
      console.log('Contract symbol:', symbol);
    });

    it('should get total supply', async function() {
      const totalSupply = await registry.totalSupply();
      expect(totalSupply).to.be.a('bigint');
      console.log('Total supply:', totalSupply.toString());
    });

    it('should get TLD contract address', async function() {
      const tldContract = await registry.getTLDContract();
      expect(tldContract).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('TLD contract:', tldContract);
    });

    it('should get Resolver contract address', async function() {
      const resolverContract = await registry.getResolverContract();
      expect(resolverContract).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Resolver contract:', resolverContract);
    });

    it('should get contract owner', async function() {
      const owner = await registry.owner();
      expect(owner).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Contract owner:', owner);
    });

    it('should check if contract is paused', async function() {
      const paused = await registry.paused();
      expect(paused).to.be.a('boolean');
      console.log('Contract paused:', paused);
    });

    it('should get contract balance', async function() {
      const balance = await registry.getBalance();
      expect(balance).to.be.a('bigint');
      console.log('Contract balance:', balance.toString());
    });
  });
});

