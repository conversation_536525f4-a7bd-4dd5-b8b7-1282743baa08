/**
 * Name Resolution Example
 * Demonstrates how to resolve names and reverse resolve addresses
 */

const ODudeSDK = require('../src/index');

async function main() {
  console.log('=== ODude Name Resolution Example ===\n');

  // Initialize SDK
  const sdk = new ODudeSDK({
    rpcUrl: 'http://127.0.0.1:8545'
  });

  sdk.connectNetwork('localhost');
  console.log('✓ Connected to contracts\n');

  // Test names to resolve
  const testNames = ['crypto', 'bitcoin@crypto', 'test@crypto'];
  
  console.log('--- Forward Resolution (Name → Address) ---');
  for (const name of testNames) {
    try {
      const exists = await sdk.resolver.nameExists(name);
      console.log(`\nName: ${name}`);
      console.log(`Exists: ${exists}`);
      
      if (exists) {
        const address = await sdk.resolve(name);
        console.log(`Resolved Address: ${address}`);
        
        // Get full resolution record
        const record = await sdk.resolver.getResolutionRecord(name);
        console.log(`Token ID: ${record.tokenId.toString()}`);
      }
    } catch (error) {
      console.log(`Failed to resolve "${name}":`, error.message);
    }
  }

  console.log('\n--- Reverse Resolution (Address → Name) ---');
  
  // Test addresses
  const testAddresses = [
    '******************************************', // Default hardhat account
    '******************************************'
  ];

  for (const address of testAddresses) {
    try {
      const hasReverse = await sdk.resolver.hasReverse(address);
      console.log(`\nAddress: ${address}`);
      console.log(`Has Reverse: ${hasReverse}`);
      
      if (hasReverse) {
        const name = await sdk.reverse(address);
        console.log(`Primary Name: ${name}`);
        
        // Get full reverse record
        const record = await sdk.resolver.getReverseRecord(address);
        console.log(`Token ID: ${record.primaryTokenId.toString()}`);
      }
    } catch (error) {
      console.log(`Failed to reverse resolve ${address}:`, error.message);
    }
  }

  console.log('\n--- Name Information ---');
  
  // Get comprehensive name info
  for (const name of testNames) {
    try {
      const info = await sdk.getNameInfo(name);
      console.log(`\nName: ${name}`);
      console.log('Info:', JSON.stringify(info, (key, value) =>
        typeof value === 'bigint' ? value.toString() : value
      , 2));
    } catch (error) {
      console.log(`Failed to get info for "${name}":`, error.message);
    }
  }

  console.log('\n✓ Example completed!');
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });

