const { expect } = require('chai');
const ODudeSDK = require('../src/ODudeSDK');

describe('ODude Names Operations', function() {
  let sdk;
  const testNames = ['crypto', 'bitcoin@crypto', 'test@crypto'];
  const testAddress = '******************************************';

  before(function() {
    sdk = new ODudeSDK({ rpcUrl: 'http://127.0.0.1:8545' });
    sdk.connectNetwork('localhost');
  });

  describe('Total Names Count', function() {
    it('should get total supply of names', async function() {
      const totalSupply = await sdk.registry.totalSupply();
      expect(totalSupply).to.be.a('bigint');
      console.log('Total names registered:', totalSupply.toString());
    });

    it('should get total names owned by an address', async function() {
      try {
        const totalNames = await sdk.getTotalNames(testAddress);
        expect(totalNames).to.be.a('bigint');
        console.log(`Total names owned by ${testAddress}:`, totalNames.toString());
      } catch (error) {
        console.log('Error getting total names:', error.message);
      }
    });
  });

  describe('List Names by Wallet Address', function() {
    it('should get list of names owned by an address', async function() {
      try {
        const namesList = await sdk.getNamesList(testAddress);
        expect(namesList).to.be.an('array');
        console.log('Names owned by address:');
        console.table(namesList);
        
        if (namesList.length > 0) {
          expect(namesList[0]).to.have.property('tokenId');
          expect(namesList[0]).to.have.property('name');
        }
      } catch (error) {
        console.log('Error getting names list:', error.message);
      }
    });

    it('should handle address with no names', async function() {
      const emptyAddress = '******************************************';
      const namesList = await sdk.getNamesList(emptyAddress);
      expect(namesList).to.be.an('array');
      expect(namesList).to.have.lengthOf(0);
    });
  });

  describe('Get All Names (Paginated)', function() {
    it('should get first 10 names', async function() {
      try {
        const allNames = await sdk.getAllNames(0, 10);
        expect(allNames).to.be.an('array');
        console.log('First 10 registered names:');
        console.table(allNames);
        
        if (allNames.length > 0) {
          expect(allNames[0]).to.have.property('tokenId');
          expect(allNames[0]).to.have.property('name');
          expect(allNames[0]).to.have.property('owner');
        }
      } catch (error) {
        console.log('Error getting all names:', error.message);
      }
    });

    it('should handle pagination correctly', async function() {
      const totalSupply = await sdk.registry.totalSupply();
      if (totalSupply > 5n) {
        const firstBatch = await sdk.getAllNames(0, 5);
        const secondBatch = await sdk.getAllNames(5, 5);
        
        expect(firstBatch).to.be.an('array');
        expect(secondBatch).to.be.an('array');
        
        // Ensure no overlap
        if (firstBatch.length > 0 && secondBatch.length > 0) {
          expect(firstBatch[0].tokenId).to.not.equal(secondBatch[0].tokenId);
        }
      }
    });
  });

  describe('Name Details for "bitcoin@crypto"', function() {
    const testName = 'bitcoin@crypto';

    it('should check if name exists', async function() {
      try {
        const exists = await sdk.resolver.nameExists(testName);
        expect(exists).to.be.a('boolean');
        console.log(`Name "${testName}" exists:`, exists);
      } catch (error) {
        console.log(`Error checking if "${testName}" exists:`, error.message);
      }
    });

    it('should get comprehensive name details', async function() {
      try {
        const details = await sdk.getNameDetails(testName);
        expect(details).to.be.an('object');
        expect(details).to.have.property('name');
        expect(details).to.have.property('tokenId');
        expect(details).to.have.property('owner');
        expect(details).to.have.property('metadata');
        expect(details).to.have.property('tokenURI');
        expect(details).to.have.property('exists');
        
        console.log(`Details for "${testName}":`);
        console.log(JSON.stringify(details, (key, value) =>
          typeof value === 'bigint' ? value.toString() : value
        , 2));
      } catch (error) {
        console.log(`Name "${testName}" not found (expected if not registered):`, error.message);
      }
    });

    it('should get token ID for name', async function() {
      try {
        const tokenId = await sdk.registry.getTokenId(testName);
        expect(tokenId).to.be.a('bigint');
        console.log(`Token ID for "${testName}":`, tokenId.toString());
      } catch (error) {
        console.log(`Error getting token ID for "${testName}":`, error.message);
      }
    });

    it('should get owner of name', async function() {
      try {
        const owner = await sdk.getOwner(testName);
        expect(owner).to.match(/^0x[a-fA-F0-9]{40}$/);
        console.log(`Owner of "${testName}":`, owner);
      } catch (error) {
        console.log(`Error getting owner of "${testName}":`, error.message);
      }
    });

    it('should resolve name to address', async function() {
      try {
        const resolvedAddress = await sdk.resolve(testName);
        expect(resolvedAddress).to.match(/^0x[a-fA-F0-9]{40}$/);
        console.log(`"${testName}" resolves to:`, resolvedAddress);
      } catch (error) {
        console.log(`Error resolving "${testName}":`, error.message);
      }
    });

    it('should get NFT metadata', async function() {
      try {
        const tokenId = await sdk.registry.getTokenId(testName);
        const metadata = await sdk.registry.getNFTMetadata(tokenId);
        
        expect(metadata).to.be.an('object');
        expect(metadata).to.have.property('id');
        expect(metadata).to.have.property('name');
        expect(metadata).to.have.property('isTLD');
        expect(metadata).to.have.property('parentTLD');
        
        console.log(`Metadata for "${testName}":`, metadata);
      } catch (error) {
        console.log(`Error getting metadata for "${testName}":`, error.message);
      }
    });

    it('should get token URI', async function() {
      try {
        const tokenId = await sdk.registry.getTokenId(testName);
        const tokenURI = await sdk.registry.tokenURI(tokenId);
        
        expect(tokenURI).to.be.a('string');
        console.log(`Token URI for "${testName}":`, tokenURI);
      } catch (error) {
        console.log(`Error getting token URI for "${testName}":`, error.message);
      }
    });

    it('should get approved address for token', async function() {
      try {
        const tokenId = await sdk.registry.getTokenId(testName);
        const approved = await sdk.getApproved(tokenId);
        
        expect(approved).to.match(/^0x[a-fA-F0-9]{40}$/);
        console.log(`Approved address for "${testName}":`, approved);
      } catch (error) {
        console.log(`Error getting approved address for "${testName}":`, error.message);
      }
    });
  });

  describe('Name Availability Check', function() {
    it('should check if names are available', async function() {
      const namesToCheck = ['crypto', 'bitcoin@crypto', 'test@crypto', 'available@crypto'];
      
      for (const name of namesToCheck) {
        const available = await sdk.isNameAvailable(name);
        expect(available).to.be.a('boolean');
        console.log(`"${name}" is ${available ? 'AVAILABLE' : 'TAKEN'}`);
      }
    });
  });

  describe('Reverse Resolution', function() {
    it('should check if address has reverse resolution', async function() {
      const hasReverse = await sdk.resolver.hasReverse(testAddress);
      expect(hasReverse).to.be.a('boolean');
      console.log(`Address ${testAddress} has reverse:`, hasReverse);
    });

    it('should get primary name for address', async function() {
      try {
        const hasReverse = await sdk.resolver.hasReverse(testAddress);
        if (hasReverse) {
          const primaryName = await sdk.reverse(testAddress);
          expect(primaryName).to.be.a('string');
          console.log(`Primary name for ${testAddress}:`, primaryName);
        } else {
          console.log(`No primary name set for ${testAddress}`);
        }
      } catch (error) {
        console.log('Error getting primary name:', error.message);
      }
    });
  });

  describe('Name Parsing and Utilities', function() {
    it('should parse different name formats', async function() {
      const names = ['crypto', 'bitcoin@crypto', 'test@crypto'];
      
      for (const name of names) {
        const parsed = sdk.utils.parseName(name);
        
        expect(parsed).to.have.property('full');
        expect(parsed).to.have.property('tld');
        expect(parsed).to.have.property('subdomain');
        expect(parsed).to.have.property('isTLD');
        expect(parsed).to.have.property('isSubdomain');
        
        console.log(`\nParsed "${name}":`, parsed);
      }
    });

    it('should normalize names correctly', async function() {
      const testCases = [
        { input: 'CRYPTO', expected: 'crypto' },
        { input: 'Bitcoin@Crypto', expected: 'bitcoin@crypto' },
        { input: 'TEST@CRYPTO', expected: 'test@crypto' }
      ];
      
      for (const testCase of testCases) {
        const normalized = sdk.utils.normalizeName(testCase.input);
        expect(normalized).to.equal(testCase.expected);
        console.log(`"${testCase.input}" → "${normalized}"`);
      }
    });
  });

  describe('Get Name by Token ID', function() {
    it('should get name from token ID', async function() {
      const totalSupply = await sdk.registry.totalSupply();
      
      if (totalSupply > 0n) {
        const tokenId = await sdk.registry.tokenByIndex(0);
        const name = await sdk.getNameById(tokenId);
        
        expect(name).to.be.a('string');
        console.log(`Token ID ${tokenId.toString()} → Name: "${name}"`);
      } else {
        console.log('No tokens minted yet');
      }
    });
  });
});

