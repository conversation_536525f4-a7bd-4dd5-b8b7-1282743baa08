const { expect } = require('chai');
const { JsonRpcProvider } = require('ethers');
const TLD = require('../src/contracts/TLD');

describe('TLD Contract', function() {
  let tld;
  let provider;
  const TLD_ADDRESS = '******************************************';

  before(async function() {
    // Connect to local hardhat node
    provider = new JsonRpcProvider('http://127.0.0.1:8545');
    tld = new TLD(TLD_ADDRESS, provider);
  });

  describe('Read-only functions', function() {
    it('should get base TLD price', async function() {
      const price = await tld.getBaseTLDPrice();
      expect(price).to.be.a('bigint');
      console.log('Base TLD price:', price.toString(), 'wei');
    });

    it('should get default commission', async function() {
      const commission = await tld.getDefaultCommission();
      expect(commission).to.be.a('bigint');
      console.log('Default commission:', commission.toString(), '%');
    });

    it('should get Registry contract address', async function() {
      const registryContract = await tld.getRegistryContract();
      expect(registryContract).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Registry contract:', registryContract);
    });

    it('should get contract owner', async function() {
      const owner = await tld.owner();
      expect(owner).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Contract owner:', owner);
    });
  });

  describe('TLD-specific queries', function() {
    it('should handle TLD price query for non-existent TLD', async function() {
      try {
        const price = await tld.getTLDPrice(999999);
        console.log('TLD price:', price.toString());
      } catch (error) {
        console.log('TLD price query failed (expected for non-existent TLD)');
      }
    });

    it('should handle commission query for non-existent TLD', async function() {
      try {
        const commission = await tld.getCommission(999999);
        console.log('Commission:', commission.toString());
      } catch (error) {
        console.log('Commission query failed (expected for non-existent TLD)');
      }
    });

    it('should handle TLD owner query for non-existent TLD', async function() {
      try {
        const owner = await tld.getTLDOwner(999999);
        console.log('TLD owner:', owner);
      } catch (error) {
        console.log('TLD owner query failed (expected for non-existent TLD)');
      }
    });

    it('should handle payment token query for non-existent TLD', async function() {
      try {
        const paymentToken = await tld.getPaymentToken(999999);
        console.log('Payment token:', paymentToken);
      } catch (error) {
        console.log('Payment token query failed (expected for non-existent TLD)');
      }
    });

    it('should handle TLD active status query for non-existent TLD', async function() {
      try {
        const isActive = await tld.isTLDActive(999999);
        console.log('TLD active:', isActive);
      } catch (error) {
        console.log('TLD active query failed (expected for non-existent TLD)');
      }
    });

    it('should handle TLD name query for non-existent TLD', async function() {
      try {
        const name = await tld.getTLDName(999999);
        console.log('TLD name:', name);
      } catch (error) {
        console.log('TLD name query failed (expected for non-existent TLD)');
      }
    });
  });

  describe('TLD queries for existing TLD', function() {
    it('should query TLD info if TLD exists', async function() {
      // Try with token ID 1 (first TLD if it exists)
      try {
        const [price, commission, owner, paymentToken, isActive, name] = await Promise.all([
          tld.getTLDPrice(1),
          tld.getCommission(1),
          tld.getTLDOwner(1),
          tld.getPaymentToken(1),
          tld.isTLDActive(1),
          tld.getTLDName(1)
        ]);

        console.log('TLD 1 Info:');
        console.log('  Price:', price.toString());
        console.log('  Commission:', commission.toString());
        console.log('  Owner:', owner);
        console.log('  Payment Token:', paymentToken);
        console.log('  Active:', isActive);
        console.log('  Name:', name);
      } catch (error) {
        console.log('TLD 1 does not exist yet');
      }
    });
  });

  describe('Contract instance', function() {
    it('should have correct address', function() {
      expect(tld.address).to.equal(TLD_ADDRESS);
    });

    it('should have contract instance', function() {
      expect(tld.contract).to.exist;
    });
  });

  describe('Event listeners', function() {
    it('should be able to set up event listeners', function() {
      expect(tld.onTLDPriceSet).to.be.a('function');
      expect(tld.onCommissionSet).to.be.a('function');
      expect(tld.onTLDActiveSet).to.be.a('function');
      expect(tld.removeAllListeners).to.be.a('function');
    });
  });
});

