const { expect } = require('chai');
const { JsonRpcProvider } = require('ethers');
const Registry = require('../src/contracts/Registry');

describe('Registry Contract', function() {
  let registry;
  let provider;
  const REGISTRY_ADDRESS = '******************************************';

  before(async function() {
    // Connect to local hardhat node
    provider = new JsonRpcProvider('http://127.0.0.1:8545');
    registry = new Registry(REGISTRY_ADDRESS, provider);
  });

  describe('Read-only functions', function() {
    it('should get contract name', async function() {
      const name = await registry.name();
      expect(name).to.be.a('string');
      console.log('Contract name:', name);
    });

    it('should get contract symbol', async function() {
      const symbol = await registry.symbol();
      expect(symbol).to.be.a('string');
      console.log('Contract symbol:', symbol);
    });

    it('should get total supply', async function() {
      const totalSupply = await registry.totalSupply();
      expect(totalSupply).to.be.a('bigint');
      console.log('Total supply:', totalSupply.toString());
    });

    it('should get TLD contract address', async function() {
      const tldContract = await registry.getTLDContract();
      expect(tldContract).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('TLD contract:', tldContract);
    });

    it('should get Resolver contract address', async function() {
      const resolverContract = await registry.getResolverContract();
      expect(resolverContract).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Resolver contract:', resolverContract);
    });

    it('should get contract owner', async function() {
      const owner = await registry.owner();
      expect(owner).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Contract owner:', owner);
    });

    it('should check if contract is paused', async function() {
      const paused = await registry.paused();
      expect(paused).to.be.a('boolean');
      console.log('Contract paused:', paused);
    });

    it('should get contract balance', async function() {
      const balance = await registry.getBalance();
      expect(balance).to.be.a('bigint');
      console.log('Contract balance:', balance.toString());
    });
  });

  describe('Token queries', function() {
    it('should handle non-existent token queries gracefully', async function() {
      try {
        await registry.ownerOf(999999);
        // If no error, that's fine too
      } catch (error) {
        expect(error.message).to.include('ERC721NonexistentToken');
      }
    });

    it('should get token by index if tokens exist', async function() {
      const totalSupply = await registry.totalSupply();
      if (totalSupply > 0n) {
        const tokenId = await registry.tokenByIndex(0);
        expect(tokenId).to.be.a('bigint');
        console.log('First token ID:', tokenId.toString());
      } else {
        console.log('No tokens minted yet');
      }
    });
  });

  describe('Name queries', function() {
    it('should handle non-existent name queries', async function() {
      try {
        const tokenId = await registry.getTokenId('nonexistent.test');
        console.log('Token ID for nonexistent name:', tokenId.toString());
      } catch (error) {
        // Expected to fail or return 0
        console.log('Name does not exist (expected)');
      }
    });

    it('should get owner by name for non-existent name', async function() {
      try {
        const owner = await registry.getOwnerByName('nonexistent.test');
        console.log('Owner:', owner);
      } catch (error) {
        console.log('Name lookup failed (expected for non-existent name)');
      }
    });
  });

  describe('Contract instance', function() {
    it('should have correct address', function() {
      expect(registry.address).to.equal(REGISTRY_ADDRESS);
    });

    it('should have contract instance', function() {
      expect(registry.contract).to.exist;
    });
  });

  describe('Event listeners', function() {
    it('should be able to set up event listeners', function() {
      expect(registry.onTLDMinted).to.be.a('function');
      expect(registry.onSubdomainMinted).to.be.a('function');
      expect(registry.onTransfer).to.be.a('function');
      expect(registry.removeAllListeners).to.be.a('function');
    });
  });
});

