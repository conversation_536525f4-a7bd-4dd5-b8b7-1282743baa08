const { expect } = require('chai');
const { JsonRpcProvider } = require('ethers');
const RWAirdrop = require('../src/contracts/RWAirdrop');

describe('RWAirdrop Contract', function() {
  let rwairdrop;
  let provider;
  const RWAIRDROP_ADDRESS = '******************************************';

  before(async function() {
    // Connect to local hardhat node
    provider = new JsonRpcProvider('http://127.0.0.1:8545');
    rwairdrop = new RWAirdrop(RWAIRDROP_ADDRESS, provider);
  });

  describe('Read-only functions', function() {
    it('should get contract owner', async function() {
      const owner = await rwairdrop.owner();
      expect(owner).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Contract owner:', owner);
    });

    it('should get contract balance', async function() {
      const balance = await rwairdrop.getBalance();
      expect(balance).to.be.a('bigint');
      console.log('Contract balance:', balance.toString());
    });

    it('should check if airdrop is active', async function() {
      try {
        const isActive = await rwairdrop.isAirdropActive();
        expect(isActive).to.be.a('boolean');
        console.log('Airdrop active:', isActive);
      } catch (error) {
        console.log('Airdrop active query failed:', error.message);
      }
    });

    it('should get total claimed amount', async function() {
      try {
        const totalClaimed = await rwairdrop.getTotalClaimed();
        expect(totalClaimed).to.be.a('bigint');
        console.log('Total claimed:', totalClaimed.toString());
      } catch (error) {
        console.log('Total claimed query failed:', error.message);
      }
    });

    it('should get total airdrop amount', async function() {
      try {
        const totalAmount = await rwairdrop.getTotalAirdropAmount();
        expect(totalAmount).to.be.a('bigint');
        console.log('Total airdrop amount:', totalAmount.toString());
      } catch (error) {
        console.log('Total airdrop amount query failed:', error.message);
      }
    });

    it('should get airdrop token address', async function() {
      try {
        const tokenAddress = await rwairdrop.getAirdropToken();
        expect(tokenAddress).to.match(/^0x[a-fA-F0-9]{40}$/);
        console.log('Airdrop token:', tokenAddress);
      } catch (error) {
        console.log('Airdrop token query failed:', error.message);
      }
    });

    it('should get start time', async function() {
      try {
        const startTime = await rwairdrop.getStartTime();
        expect(startTime).to.be.a('bigint');
        console.log('Start time:', startTime.toString());
      } catch (error) {
        console.log('Start time query failed:', error.message);
      }
    });

    it('should get end time', async function() {
      try {
        const endTime = await rwairdrop.getEndTime();
        expect(endTime).to.be.a('bigint');
        console.log('End time:', endTime.toString());
      } catch (error) {
        console.log('End time query failed:', error.message);
      }
    });
  });

  describe('Address-specific queries', function() {
    const testAddress = '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266';

    it('should check if address has claimed', async function() {
      try {
        const hasClaimed = await rwairdrop.hasClaimed(testAddress);
        expect(hasClaimed).to.be.a('boolean');
        console.log('Address has claimed:', hasClaimed);
      } catch (error) {
        console.log('Has claimed query failed:', error.message);
      }
    });

    it('should get claimable amount for address', async function() {
      try {
        const claimableAmount = await rwairdrop.getClaimableAmount(testAddress);
        expect(claimableAmount).to.be.a('bigint');
        console.log('Claimable amount:', claimableAmount.toString());
      } catch (error) {
        console.log('Claimable amount query failed:', error.message);
      }
    });

    it('should check if address is eligible', async function() {
      try {
        const isEligible = await rwairdrop.isEligible(testAddress);
        expect(isEligible).to.be.a('boolean');
        console.log('Address is eligible:', isEligible);
      } catch (error) {
        console.log('Is eligible query failed:', error.message);
      }
    });

    it('should get comprehensive airdrop info', async function() {
      try {
        const info = await rwairdrop.getAirdropInfo(testAddress);
        expect(info).to.be.an('object');
        expect(info).to.have.property('eligible');
        expect(info).to.have.property('claimed');
        expect(info).to.have.property('claimableAmount');
        expect(info).to.have.property('isActive');
        expect(info).to.have.property('canClaim');
        console.log('Airdrop info:', info);
      } catch (error) {
        console.log('Airdrop info query failed:', error.message);
      }
    });
  });

  describe('Contract instance', function() {
    it('should have correct address', function() {
      expect(rwairdrop.address).to.equal(RWAIRDROP_ADDRESS);
    });

    it('should have contract instance', function() {
      expect(rwairdrop.contract).to.exist;
    });
  });

  describe('Event listeners', function() {
    it('should be able to set up event listeners', function() {
      expect(rwairdrop.onClaimed).to.be.a('function');
      expect(rwairdrop.onAirdropActiveSet).to.be.a('function');
      expect(rwairdrop.onEligibleAddressAdded).to.be.a('function');
      expect(rwairdrop.removeAllListeners).to.be.a('function');
    });
  });
});

