const { expect } = require('chai');
const helpers = require('../src/utils/helpers');

describe('Helper Utilities', function() {
  describe('normalizeName', function() {
    it('should convert to lowercase', function() {
      expect(helpers.normalizeName('TEST.ODUDE')).to.equal('test.odude');
      expect(helpers.normalizeName('Alice.Bob.ODUDE')).to.equal('alice.bob.odude');
    });

    it('should trim whitespace', function() {
      expect(helpers.normalizeName('  test.odude  ')).to.equal('test.odude');
    });

    it('should throw on invalid input', function() {
      expect(() => helpers.normalizeName('')).to.throw('Invalid name');
      expect(() => helpers.normalizeName(null)).to.throw('Invalid name');
      expect(() => helpers.normalizeName(123)).to.throw('Invalid name');
    });
  });

  describe('extractTLD', function() {
    it('should extract TLD from simple name', function() {
      expect(helpers.extractTLD('odude')).to.equal('odude');
    });

    it('should extract TLD from subdomain', function() {
      expect(helpers.extractTLD('alice.odude')).to.equal('odude');
      expect(helpers.extractTLD('bob.alice.odude')).to.equal('odude');
    });

    it('should handle uppercase', function() {
      expect(helpers.extractTLD('ALICE.ODUDE')).to.equal('odude');
    });
  });

  describe('extractSubdomain', function() {
    it('should return null for TLD', function() {
      expect(helpers.extractSubdomain('odude')).to.be.null;
    });

    it('should extract subdomain', function() {
      expect(helpers.extractSubdomain('alice.odude')).to.equal('alice');
      expect(helpers.extractSubdomain('bob.alice.odude')).to.equal('bob.alice');
    });
  });

  describe('isTLD', function() {
    it('should identify TLD', function() {
      expect(helpers.isTLD('odude')).to.be.true;
      expect(helpers.isTLD('test')).to.be.true;
    });

    it('should identify non-TLD', function() {
      expect(helpers.isTLD('alice.odude')).to.be.false;
      expect(helpers.isTLD('bob.alice.odude')).to.be.false;
    });
  });

  describe('isSubdomain', function() {
    it('should identify subdomain', function() {
      expect(helpers.isSubdomain('alice.odude')).to.be.true;
      expect(helpers.isSubdomain('bob.alice.odude')).to.be.true;
    });

    it('should identify non-subdomain', function() {
      expect(helpers.isSubdomain('odude')).to.be.false;
    });
  });

  describe('parseName', function() {
    it('should parse TLD', function() {
      const parsed = helpers.parseName('odude');
      expect(parsed.full).to.equal('odude');
      expect(parsed.tld).to.equal('odude');
      expect(parsed.subdomain).to.be.null;
      expect(parsed.parts).to.deep.equal(['odude']);
      expect(parsed.isTLD).to.be.true;
      expect(parsed.isSubdomain).to.be.false;
    });

    it('should parse subdomain', function() {
      const parsed = helpers.parseName('alice.odude');
      expect(parsed.full).to.equal('alice.odude');
      expect(parsed.tld).to.equal('odude');
      expect(parsed.subdomain).to.equal('alice');
      expect(parsed.parts).to.deep.equal(['alice', 'odude']);
      expect(parsed.isTLD).to.be.false;
      expect(parsed.isSubdomain).to.be.true;
    });

    it('should parse nested subdomain', function() {
      const parsed = helpers.parseName('bob.alice.odude');
      expect(parsed.full).to.equal('bob.alice.odude');
      expect(parsed.tld).to.equal('odude');
      expect(parsed.subdomain).to.equal('bob.alice');
      expect(parsed.parts).to.deep.equal(['bob', 'alice', 'odude']);
      expect(parsed.isTLD).to.be.false;
      expect(parsed.isSubdomain).to.be.true;
    });
  });

  describe('isValidAddress', function() {
    it('should validate correct addresses', function() {
      expect(helpers.isValidAddress('0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266')).to.be.true;
      expect(helpers.isValidAddress('0x0000000000000000000000000000000000000000')).to.be.true;
    });

    it('should reject invalid addresses', function() {
      expect(helpers.isValidAddress('0xinvalid')).to.be.false;
      expect(helpers.isValidAddress('not an address')).to.be.false;
      expect(helpers.isValidAddress('0xf39Fd6e51aad88F6F4ce6aB8827279cffFb9226')).to.be.false; // too short
    });
  });

  describe('formatTokenId', function() {
    it('should format bigint', function() {
      expect(helpers.formatTokenId(123n)).to.equal('123');
    });

    it('should format number', function() {
      expect(helpers.formatTokenId(456)).to.equal('456');
    });

    it('should format string', function() {
      expect(helpers.formatTokenId('789')).to.equal('789');
    });
  });

  describe('parseEther and formatEther', function() {
    it('should parse ether to wei', function() {
      const wei = helpers.parseEther('1.0');
      expect(wei).to.equal(1000000000000000000n);
    });

    it('should format wei to ether', function() {
      const ether = helpers.formatEther(1000000000000000000n);
      expect(ether).to.equal('1.0');
    });

    it('should handle decimal values', function() {
      const wei = helpers.parseEther('0.5');
      expect(wei).to.equal(500000000000000000n);
      
      const ether = helpers.formatEther(500000000000000000n);
      expect(ether).to.equal('0.5');
    });
  });
});

