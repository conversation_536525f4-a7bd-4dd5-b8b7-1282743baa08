const { expect } = require('chai');
const { JsonRpcProvider } = require('ethers');
const Resolver = require('../src/contracts/Resolver');

describe('Resolver Contract', function() {
  let resolver;
  let provider;
  const RESOLVER_ADDRESS = '******************************************';

  before(async function() {
    // Connect to local hardhat node
    provider = new JsonRpcProvider('http://127.0.0.1:8545');
    resolver = new Resolver(RESOLVER_ADDRESS, provider);
  });

  describe('Read-only functions', function() {
    it('should get Registry contract address', async function() {
      const registryContract = await resolver.getRegistryContract();
      expect(registryContract).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Registry contract:', registryContract);
    });

    it('should get contract owner', async function() {
      const owner = await resolver.owner();
      expect(owner).to.match(/^0x[a-fA-F0-9]{40}$/);
      console.log('Contract owner:', owner);
    });
  });

  describe('Name resolution', function() {
    it('should check if name exists', async function() {
      const exists = await resolver.nameExists('test.odude');
      expect(exists).to.be.a('boolean');
      console.log('Name "test.odude" exists:', exists);
    });

    it('should handle non-existent name resolution', async function() {
      try {
        const address = await resolver.resolve('nonexistent.test');
        console.log('Resolved address:', address);
        // If it returns zero address, that's expected
        if (address === '0x0000000000000000000000000000000000000000') {
          console.log('Name resolves to zero address (not set)');
        }
      } catch (error) {
        console.log('Name resolution failed (expected for non-existent name)');
      }
    });

    it('should get resolution record', async function() {
      const record = await resolver.getResolutionRecord('test.odude');
      expect(record).to.be.an('object');
      expect(record).to.have.property('resolvedAddress');
      expect(record).to.have.property('name');
      expect(record).to.have.property('tokenId');
      expect(record).to.have.property('exists');
      console.log('Resolution record:', record);
    });
  });

  describe('Reverse resolution', function() {
    it('should check if address has reverse', async function() {
      const testAddress = '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266';
      const hasReverse = await resolver.hasReverse(testAddress);
      expect(hasReverse).to.be.a('boolean');
      console.log('Address has reverse:', hasReverse);
    });

    it('should get reverse record', async function() {
      const testAddress = '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266';
      const record = await resolver.getReverseRecord(testAddress);
      expect(record).to.be.an('object');
      expect(record).to.have.property('primaryName');
      expect(record).to.have.property('primaryTokenId');
      expect(record).to.have.property('exists');
      console.log('Reverse record:', record);
    });

    it('should handle reverse resolution', async function() {
      const testAddress = '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266';
      try {
        const name = await resolver.reverse(testAddress);
        console.log('Reverse resolved name:', name);
      } catch (error) {
        console.log('Reverse resolution failed (expected if not set)');
      }
    });
  });

  describe('Token ID queries', function() {
    it('should get token ID for name', async function() {
      try {
        const tokenId = await resolver.getTokenId('test.odude');
        expect(tokenId).to.be.a('bigint');
        console.log('Token ID:', tokenId.toString());
      } catch (error) {
        console.log('Token ID lookup failed (expected for non-existent name)');
      }
    });

    it('should get name for token ID', async function() {
      try {
        const name = await resolver.getName(1);
        console.log('Name for token ID 1:', name);
      } catch (error) {
        console.log('Name lookup failed (expected if token does not exist)');
      }
    });
  });

  describe('Contract instance', function() {
    it('should have correct address', function() {
      expect(resolver.address).to.equal(RESOLVER_ADDRESS);
    });

    it('should have contract instance', function() {
      expect(resolver.contract).to.exist;
    });
  });

  describe('Event listeners', function() {
    it('should be able to set up event listeners', function() {
      expect(resolver.onNameResolved).to.be.a('function');
      expect(resolver.onReverseSet).to.be.a('function');
      expect(resolver.onReverseRemoved).to.be.a('function');
      expect(resolver.removeAllListeners).to.be.a('function');
    });
  });
});

